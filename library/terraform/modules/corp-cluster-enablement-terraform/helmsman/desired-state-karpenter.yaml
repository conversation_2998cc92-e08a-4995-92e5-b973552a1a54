metadata:
  description: "VIAB Stack Infratructure core EKS Services"

context: stack-infrastructure

namespaces:
  kube-system:

helmRepos:
  karpenter: "https://charts.karpenter.sh"

appsTemplates:
  stack-infrastructure: &stack-infrastructure
    group: stack-infrastructure
    enabled: true
    wait: true
    timeout: 1800 # 30 minutes 
    namespace: kube-system

apps:
  lights-out-crd:
    <<: *stack-infrastructure
    enabled: ${LIGHTS_OUT_ENABLED}
    name: lights-out-crd
    description: "CustomResourcDefinition for Vercaode Karpenter LightsOut"
    chart: local-charts/lights-out-crd
    version: "1.0.0"
    priority: -9
    valuesFiles:
      - values/lights-out-crd.yaml

  lights-out-operator:
    <<: *stack-infrastructure
    enabled: ${LIGHTS_OUT_ENABLED}
    name: lights-out-operator
    description: "Operator configuration for LightsOut"
    chart: local-charts/lights-out-operator
    version: "1.0.0"
    priority: -8
    valuesFiles:
      - values/lights-out-operator.yaml

  lights-out-policy:
    <<: *stack-infrastructure
    enabled: ${LIGHTS_OUT_ENABLED}
    name: lights-out-policy
    description: "Policy configuration for LightsOut"
    chart: local-charts/lights-out-policy
    version: "1.0.0"
    priority: -7
    valuesFiles:
      - values/lights-out-policy.yaml

  karpenter:
    <<: *stack-infrastructure
    enabled: true
    name: karpenter
    description: "A Helm chart for Karpenter, an open-source node provisioning project built for Kubernetes."
    chart: "${CHART_SOURCE_KARPENTER}"
    version: "1.2.1"
    priority: -10
    valuesFiles:
      - values/karpenter.yaml

  karpenter-nodepools:
    <<: *stack-infrastructure
    enabled: true
    name: karpenter-nodepools
    description: "NodePools for managing workloads"
    chart: ./local-charts/karpenter-nodepools
    version: "1.0.0"
    priority: -9
    valuesFiles:
      - values/karpenter-nodepools.yaml
